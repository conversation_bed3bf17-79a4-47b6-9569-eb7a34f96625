# 文件传输客户端演示应用

这是一个完整的文件传输客户端SDK演示应用，展示了如何使用文件传输客户端SDK进行各种文件传输操作。

## 功能特性

- ✅ **文件上传演示**: 展示单文件和多文件上传功能
- ✅ **文件下载演示**: 展示文件下载和断点续传功能
- ✅ **文件信息查询**: 展示文件元数据查询功能
- ✅ **断点续传演示**: 展示大文件断点续传功能
- ✅ **并发传输演示**: 展示多文件并发传输功能
- ✅ **错误处理演示**: 展示各种错误情况的处理
- ✅ **性能测试演示**: 展示传输性能测试和统计
- ✅ **进度监控**: 实时显示传输进度和速度
- ✅ **统计报告**: 生成详细的传输统计报告

## 快速开始

### 1. 前置条件

确保已经启动了文件传输服务器：

```bash
# 启动演示服务器
cd ../file-transfer-server-standalone
mvn spring-boot:run

# 或者启动独立服务器
cd ../file-transfer-server-standalone
./start-server.sh start --background
```

### 2. 运行演示应用

```bash
# 使用Maven运行
mvn exec:java

# 或者编译后运行JAR包
mvn clean package
java -jar target/file-transfer-client-demo-1.0.0.jar
```

### 3. 自定义配置运行

```bash
# 指定服务器地址和端口
mvn exec:java \
  -Ddemo.server.host=localhost \
  -Ddemo.server.port=49011 \
  -Ddemo.user.name=demo \
  -Ddemo.user.secret=demo-secret-key-2024

# 指定文件目录
mvn exec:java \
  -Ddemo.upload.dir=custom-upload \
  -Ddemo.download.dir=custom-download

# 指定传输参数
mvn exec:java \
  -Ddemo.chunk.size=2097152 \
  -Ddemo.max.concurrent.transfers=5 \
  -Ddemo.retry.count=3
```

## 配置参数

### 服务器连接配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `demo.server.host` | localhost | 服务器主机地址 |
| `demo.server.port` | 49011 | 服务器端口 |
| `demo.user.name` | demo | 用户名（与服务端配置的demo用户对应） |
| `demo.user.secret` | demo-secret-key-2024 | 用户密钥（必须与服务端demo用户的secret-key一致） |

### 文件目录配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `demo.upload.dir` | demo-files/upload | 上传文件目录 |
| `demo.download.dir` | demo-files/download | 下载文件目录 |

### 传输配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `demo.chunk.size` | 1048576 | 分块大小（字节）|
| `demo.max.concurrent.transfers` | 3 | 最大并发传输数 |
| `demo.retry.count` | 3 | 重试次数 |
| `demo.retry.delay` | 1000 | 重试延迟（毫秒）|

### 测试文件大小配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `demo.small.file.size` | 10240 | 小文件大小（字节）|
| `demo.medium.file.size` | 1048576 | 中等文件大小（字节）|
| `demo.large.file.size` | 10485760 | 大文件大小（字节）|

## 演示流程

演示应用会按照以下顺序执行各种功能演示：

### 1. 文件上传演示
- 创建不同大小的测试文件
- 演示单文件上传功能
- 显示上传进度和传输速度
- 验证上传结果

### 2. 文件下载演示
- 下载已上传的文件
- 显示下载进度和传输速度
- 验证文件完整性

### 3. 文件信息查询演示
- 查询文件元数据信息
- 显示文件ID、名称、大小、类型等信息

### 4. 断点续传演示
- 创建大文件进行断点续传测试
- 演示分片下载功能
- 验证文件完整性

### 5. 并发传输演示
- 创建多个文件进行并发上传
- 测试并发传输性能
- 统计并发传输结果

### 6. 错误处理演示
- 测试无效文件ID下载
- 测试不存在文件上传
- 测试无效路径下载
- 验证错误处理机制

### 7. 性能测试演示
- 使用大文件进行性能测试
- 测量上传下载吞吐量
- 生成性能统计报告

## 输出示例

```
========================================
    文件传输客户端SDK演示应用
    版本: 1.0.0
    时间: 2024-06-19T18:30:00
========================================

演示配置:
  服务器地址: localhost:49011
  用户名称: demo
  分块大小: 1.0 MB
  最大并发: 3
  重试次数: 3

开始运行文件传输演示序列...

=== 1. 文件上传演示 ===
上传文件: small-test.txt (10.0 KB)
开始传输: small-test.txt (10.0 KB)
进度: [====================] 100.0% (10.0 KB/10.0 KB) 速度: 2.5 MB/s 
✅ 传输完成: small-test.txt, 耗时: 45毫秒, 平均速度: 2.2 MB/s
✅ 上传成功 - 文件ID: file_12345, 耗时: 52ms

=== 2. 文件下载演示 ===
下载文件ID: file_12345
开始传输: download-test.txt (1.0 MB)
进度: [====================] 100.0% (1.0 MB/1.0 MB) 速度: 8.5 MB/s 
✅ 传输完成: download-test.txt, 耗时: 125毫秒, 平均速度: 8.0 MB/s
✅ 下载成功 - 保存路径: demo-files/download/downloaded-download-test.txt, 耗时: 135ms

...

========================================
           演示结果统计
========================================
上传统计:
  成功次数: 5
  失败次数: 0
  总上传量: 12.5 MB
  总耗时: 1250ms
  平均速度: 10.0 MB/s
  最大速度: 15.2 MB/s

下载统计:
  成功次数: 3
  失败次数: 0
  总下载量: 12.5 MB
  总耗时: 980ms
  平均速度: 12.8 MB/s
  最大速度: 18.5 MB/s

其他操作统计:
  信息查询成功: 2
  信息查询失败: 0
  错误处理测试: 3
  性能测试次数: 1
  并发传输次数: 1
  断点续传次数: 1

总体成功率: 100.00% (13/13)
========================================
```

## 日志文件

演示应用会生成详细的日志文件：

- **控制台日志**: 实时显示演示进度和结果
- **文件日志**: `logs/client-demo.log` - 详细的执行日志
- **滚动日志**: 按日期和大小自动滚动，保留30天历史

## 故障排除

### 常见问题

1. **连接服务器失败**
   - 确保服务器已启动并运行在正确端口
   - 检查网络连接和防火墙设置
   - 验证服务器地址和端口配置

2. **文件上传失败**
   - 检查上传目录权限
   - 确保磁盘空间充足
   - 验证文件大小限制

3. **文件下载失败**
   - 检查下载目录权限
   - 确保文件ID有效
   - 验证网络连接稳定性

4. **演示文件创建失败**
   - 检查演示目录权限
   - 确保磁盘空间充足
   - 验证文件系统支持

### 调试模式

启用详细日志输出：

```bash
mvn exec:java -Dlogging.level.com.sdesrd.filetransfer=DEBUG
```

### 清理演示文件

演示应用会自动清理生成的测试文件，如需手动清理：

```bash
rm -rf demo-files/
rm -rf logs/
```

## 集成到构建流程

演示应用已集成到项目的自动化构建和测试流程中：

```bash
# 运行完整构建和测试（包含演示测试）
./build-and-test.sh

# 仅运行集成测试（包含演示测试）
./build-and-test.sh --integration-only
```

## 技术实现

### 核心类

- **FileTransferClientDemo**: 主演示类，协调各种演示功能
- **DemoTransferListener**: 传输监听器，处理进度显示和事件
- **DemoStatistics**: 统计收集器，生成演示结果报告

### 设计特点

- **模块化设计**: 每个演示功能独立实现，便于维护
- **配置驱动**: 通过系统属性灵活配置演示参数
- **错误处理**: 完善的异常处理和错误恢复机制
- **资源管理**: 自动清理临时文件和资源
- **进度监控**: 实时显示传输进度和性能指标

## 扩展开发

如需添加新的演示功能：

1. 在 `FileTransferClientDemo` 中添加新的演示方法
2. 在 `runDemoSequence()` 中调用新方法
3. 在 `DemoStatistics` 中添加相应的统计功能
4. 更新配置参数和文档

## 相关文档

- [文件传输SDK用户指南](../README.md)
- [客户端SDK API文档](../file-transfer-client-sdk/README.md)
- [服务端SDK API文档](../file-transfer-server-sdk/README.md)
- [独立服务端部署指南](../file-transfer-server-standalone/README.md)
